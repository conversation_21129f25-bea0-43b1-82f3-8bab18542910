package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type SysTenantHandler struct {
	*Handler
	sysTenantService service.SysTenantService
}

func NewSysTenantHandler(
	handler *Handler,
	sysTenantService service.SysTenantService,
) *SysTenantHandler {
	return &SysTenantHandler{
		Handler:          handler,
		sysTenantService: sysTenantService,
	}
}

// Create godoc
// @Summary 创建租户
// @Schemes
// @Description 创建新的租户记录
// @Tags 系统模块,租户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.SysTenantCreateParams true "租户信息"
// @Success 200 {object} v1.Response
// @Router /sys/tenants [post]
func (h *SysTenantHandler) Create(ctx *gin.Context) {
	var req v1.SysTenantCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysTenantService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新租户
// @Schemes
// @Description 更新指定ID的租户信息
// @Tags 系统模块,租户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "租户ID"
// @Param request body v1.SysTenantUpdateParams true "租户信息"
// @Success 200 {object} v1.Response
// @Router /sys/tenants/{id} [patch]
func (h *SysTenantHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.SysTenantUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysTenantService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除租户
// @Schemes
// @Description 删除指定ID的租户
// @Tags 系统模块,租户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "租户ID"
// @Success 200 {object} v1.Response
// @Router /sys/tenants/{id} [delete]
func (h *SysTenantHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysTenantService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除租户
// @Schemes
// @Description 批量删除指定IDs的租户
// @Tags 系统模块,租户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "租户IDs"
// @Success 200 {object} v1.Response
// @Router /sys/tenants [delete]
func (h *SysTenantHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysTenantService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取租户
// @Schemes
// @Description 获取指定ID的租户信息
// @Tags 系统模块,租户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "租户ID"
// @Success 200 {object} v1.Response{data=v1.SysTenantResponse}
// @Router /sys/tenants/{id} [get]
func (h *SysTenantHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	tenant, err := h.sysTenantService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, tenant)
}

// List godoc
// @Summary 获取租户列表
// @Schemes
// @Description 分页获取租户列表
// @Tags 系统模块,租户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param name query string false "名称筛选" example:"租户名称"
// @Param code query string false "编码筛选" example:"tenant-code"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.SysTenantResponse}}
// @Router /sys/tenants [get]
func (h *SysTenantHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.sysTenantService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}

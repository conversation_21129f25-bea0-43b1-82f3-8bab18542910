package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

type SysTenantService interface {
	Create(ctx context.Context, req *v1.SysTenantCreateParams) error
	Update(ctx context.Context, id uint, req *v1.SysTenantUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.SysTenantResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewSysTenantService(
	service *Service,
	sysTenantRepository repository.SysTenantRepository,
) SysTenantService {
	return &sysTenantService{
		Service:             service,
		sysTenantRepository: sysTenantRepository,
	}
}

type sysTenantService struct {
	*Service
	sysTenantRepository repository.SysTenantRepository
}

// 租户相关方法实现
func (s *sysTenantService) Create(ctx context.Context, req *v1.SysTenantCreateParams) error {
	tenant := &model.SysTenant{}
	if err := copier.Copy(tenant, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysTenantRepository.Create(ctx, tenant); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysTenantService) Update(ctx context.Context, id uint, req *v1.SysTenantUpdateParams) error {
	tenant, err := s.sysTenantRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	if err := copier.Copy(tenant, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysTenantRepository.Update(ctx, tenant); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysTenantService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysTenantRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysTenantService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysTenantRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysTenantService) Get(ctx context.Context, id uint) (*v1.SysTenantResponse, error) {
	tenant, err := s.sysTenantRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	response := &v1.SysTenantResponse{}
	if err := copier.Copy(response, tenant); err != nil {
		return nil, err
	}

	response.CreatedAt = tenant.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = tenant.UpdatedAt.Format(time.RFC3339)
	response.ExpiredAt = tenant.ExpiredAt.Format(time.RFC3339)

	return response, nil
}

func (s *sysTenantService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	name := ctx.(*gin.Context).DefaultQuery("name", "")
	if name != "" {
		params.AddFilter("name_like", name)
	}

	// 编码筛选
	code := ctx.(*gin.Context).DefaultQuery("code", "")
	if code != "" {
		params.AddFilter("code", code)
	}

	tenants, total, err := s.sysTenantRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.SysTenantResponse, 0, len(tenants))
	for _, tenant := range tenants {
		response := &v1.SysTenantResponse{}
		if err := copier.Copy(response, tenant); err != nil {
			return nil, err
		}

		response.CreatedAt = tenant.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = tenant.UpdatedAt.Format(time.RFC3339)
		response.ExpiredAt = tenant.ExpiredAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
